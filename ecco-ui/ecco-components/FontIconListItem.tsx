import * as React from "react";
import {FC, forwardRef, useMemo} from "react";
import {Icon, ListItem, ListItemIcon, ListItemText} from "@eccosolutions/ecco-mui";
import {Link, LinkProps} from "react-router-dom";
import type {LocationDescriptor} from "history"

export const FontIconListItem: FC<{
    text: string;
    secondaryText?: string;
    selected?: boolean;
    disabled?: boolean;
    /** If a click handler is provided then the list item will have button prop set */
    onClick?: () => void;
    to?: string | LocationDescriptor;
    iconClasses: string;
    backgroundColour?: string;
    subSection?: string | null;
}> = props => {
    const renderLink = props.to
        ? useMemo(
              () =>
                  forwardRef<any, Omit<LinkProps, "to">>((itemProps, ref) => (
                      <Link to={props.to!} ref={ref} {...itemProps} />
                  )),
              [props.to]
          )
        : undefined;

    // Define sub-section styling
    const getSubSectionStyle = () => {
        if (!props.subSection)
            return props.backgroundColour ? {backgroundColor: props.backgroundColour} : undefined;

        const baseStyle = {
            paddingLeft: 24 // Add indentation for sub-menu items
        };

        switch (props.subSection) {
            case "clients":
                return {
                    ...baseStyle,
                    borderLeft: "3px solid #2994ed"
                };
            case "buildings":
                return {
                    ...baseStyle,
                    borderLeft: "3px solid #36720A"
                };
            default:
                return baseStyle;
        }
    };

    return (
        <ListItem
            button
            component={renderLink!}
            key={props.text}
            disabled={props.disabled || undefined}
            style={getSubSectionStyle()}
            selected={props.selected}
            onClick={() => props.disabled != true && props.onClick && props.onClick()}
        >
            <ListItemText secondary={props.secondaryText}>{props.text}</ListItemText>
            <ListItemIcon>
                <Icon className={props.iconClasses} />
            </ListItemIcon>
        </ListItem>
    );
};
FontIconListItem.displayName = "FontIconListItem"
