import * as React from "react";
import {Divider} from "@eccosolutions/ecco-mui";
import {List} from "@eccosolutions/ecco-mui";
import {ListSubheader} from "@eccosolutions/ecco-mui";
import {applicationRootPath} from "application-properties";
import {ReactNode} from "react";
import {useRouteMatch} from "react-router";
import {FontIconListItem} from "./FontIconListItem";

export interface MenuItem {
    /** If a click handler is provided then the list item will have button prop set */
    onClick: () => void
    iconClasses: string
    text: string
    secondaryText?: string
    disabled?: boolean
}

const onExternal = (path: string) => {
    window.location.href = applicationRootPath + path;
};

const OwnRouteMenuItem = (props: {
    dest: string;
    destQuery?: string;
    text: string;
    iconClasses: string;
    secondaryText?: string;
    disabled?: boolean;
    backgroundColour?: string;
    subSection?: string | null;
}) => {
    const match = useRouteMatch({path: props.dest, exact: true});
    const selected = match != null;

    // see https://reacttraining.com/react-router/web/api/Link
    return (
        <FontIconListItem
            to={{pathname: props.dest, search: props.destQuery}}
            {...props}
            selected={selected}
            subSection={props.subSection}
        />
    );
};

export class SidebarMenuBuilder {
    // This could impl an interface that allows addMenu, addDivider for different output
    private sidebarItems: ReactNode[] = [];
    private currentSubSection: string | null = null;

    constructor(private routerPath: string, routeMatch?: any) {
        console.assert(routerPath.endsWith("/"), routerPath);
        console.assert(routerPath.indexOf("//") < 0, routerPath);
        console.debug(`MenuBuilder with routerPath: "${routerPath}", routeMatch: %o`, routeMatch);
    }

    addSubHeader(subHeader: string, condition = true, backgroundColour: string | null = null) {
        if (condition) {
            this.currentSubSection = subHeader;

            // Define sub-header styling with left border accent
            const getSubHeaderStyle = () => {
                const baseStyle = {
                    paddingLeft: 16,
                    fontWeight: 600
                };

                switch (subHeader) {
                    case "clients":
                        return {
                            ...baseStyle,
                            borderLeft: "10px solid #2994ed"
                        };
                    case "buildings":
                        return {
                            ...baseStyle,
                            borderLeft: "10px solid #36720A"
                        };
                    default:
                        return baseStyle;
                }
            };

            this.sidebarItems.push(
                <ListSubheader key={subHeader} style={getSubHeaderStyle()}>
                    {subHeader}
                </ListSubheader>
            );
        }
        return this;
    }

    /** Add a link to a route relative to applicationRootPath */
    addExternalRoute(
        text: string,
        iconClasses: string,
        path: string,
        condition: boolean = true,
        secondaryText?: string,
        disabled?: boolean
    ) {
        if (condition) {
            console.assert(!path.startsWith("/"), path);
            this.sidebarItems.push(
                <FontIconListItem
                    {...{text, iconClasses, secondaryText, disabled}}
                    key={path}
                    onClick={() => onExternal(path)}
                />
            );
        }
        return this;
    }

    /** Add a link to a react-router route using path relative to the browser router, or current page */
    addOwnRoute(
        text: string,
        iconClasses: string,
        path: string,
        condition: boolean = true,
        secondaryText?: string,
        disabled?: boolean,
        backgroundColour?: string
    ) {
        if (condition) {
            console.assert(!path.startsWith("/"), path);
            const paths = path.split("?");
            this.sidebarItems.push(
                <OwnRouteMenuItem
                    key={path}
                    dest={this.routerPath + paths[0]}
                    destQuery={paths.length > 1 ? paths[1] : undefined}
                    text={text}
                    iconClasses={iconClasses}
                    secondaryText={secondaryText}
                    disabled={disabled}
                    backgroundColour={backgroundColour}
                    subSection={this.currentSubSection}
                />
            );
        }
        return this;
    }

    /** Add menu item if condition is true */
    addMenu(
        text: string,
        iconClasses: string,
        onClick: () => void,
        condition: boolean = true,
        secondaryText?: string,
        disabled?: boolean
    ) {
        if (condition) {
            this.sidebarItems.push(
                <FontIconListItem
                    {...{text, iconClasses, onClick, secondaryText, disabled}}
                    key={text}
                />
            ); // TODO: Check JS output isn't verbose from using spread operator
        }
        return this;
    }

    /** Allow self-contained components to be added.
     * Should generally be a FontIconListItem */
    addComponent(componentFactory: () => ReactNode, condition: boolean = true) {
        if (condition) {
            this.sidebarItems.push(componentFactory());
        }
        return this;
    }

    addDivider(key: string, condition = true) {
        /* ensure you hide these when not useful by adding this style to the CSS
          hr.MuiDivider-root:last-child, hr.MuiDivider-root + hr.MuiDivider-root {
              display: none;
          }
        */
        if (condition) {
            this.currentSubSection = null; // Reset sub-section when adding divider
            this.sidebarItems.push(<Divider key={key} />);
        }
        return this;
    }

    build() {
        return <List key={`${this.routerPath}-sidebar`}>{this.sidebarItems}</List>;
    }
}