import $ = require("jquery");
import {Box, Grid, useMediaQuery} from "@eccosolutions/ecco-mui";

import {
    AppBarBase,
    AppBarContextProvider,
    AppBilling,
    AsyncSessionData, blueAppBarLight, CareVisitMenuContextProvider,
    ContractsList, greenAppBarLight, GuidanceMenu, QRScanMenu,
    RouteFallbackWithDiagnostics,
    SidebarMenuBuilder,
    useAdminModeMenu, useAppBarContext,
    useAppBarOptions,
    useQuery,
    UserMenu,
    useServicesContext
} from "ecco-components";
import {SelectionCriteria, SessionData} from "ecco-dto";
import * as React from "react";
import {FC, useRef} from "react";
import {Route, Switch, useHistory, useParams, useRouteMatch} from "react-router";
import {MenuUpdateEvent} from "../common/events";
import {lazyControlWrapper} from "../components/ControlWrapper";
import {EventBusDisplayContainer} from "../components/EventBusDisplayContainer";
import {OverviewSummary} from "../controls/layout/OverviewSummary";
import SessionDataService from "../feature-config/SessionDataService";
import {ServicesContextProvider} from "../offline/ServicesContextProvider";
import WelcomePage from "./WelcomePage";
import {CarePage} from "../care/CarePage";
import {CalendarWithStaffOverlay} from "ecco-calendar";
import {GroupSupportActivityOverviewModal} from "../groupsupport/GroupSupportActivityOverviewModal";
import {GroupSupportActivityList} from "../groupsupport/GroupSupportActivityList";
import {DailyChecksPage} from "../dailyChecks/DailyChecksPage";
import {MyReferralsList} from "../referral/components/MyReferralsList";
import {ServicesProjectsBuildingsSelector} from "ecco-components";
import {SchedulerView} from "ecco-components";
import {GroupCommsActivityList} from "../groupsupport/GroupCommsActivityList";
import {GroupCommsActivityOverviewModal} from "../groupsupport/GroupCommsActivityOverviewModal";
import {GroupAuxActivityOverviewModal} from "../groupsupport/GroupAuxActivityOverviewModal";
import {IncidentSchemaList} from "ecco-incidents";
import {RepairSchemaList} from "ecco-repairs";
import {EventPage} from "../calendar/EventPage";
import {NewIncident} from "../incidents/inbound/NewIncident";
import {OccupancyList} from "ecco-components";

const Chart = React.lazy(() => import("../components/Chart"));
const ContractView = React.lazy(() => import("../contracts/ContractView"));
//const StaffSearchPage = React.lazy(() => import("../hr/StaffSearchPage"));
const RotaView = React.lazy(() => import("../rota/RotaView"));
const ShiftView = React.lazy(() => import("../rota/components/ShiftView"));
const BuildingView = React.lazy(() => import("../rota/components/BuildingView"));

// @ts-ignore
const ReportsControl = lazyControlWrapper(() => import("../reports/charts/ChartListControl"), undefined);
const ReportsList: FC = () => {
    useAdminModeMenu("manage reports");
    useAppBarOptions("reports");
    return <ReportsControl/>;
};

// load the buildings - default/no arguments
// @ts-ignore - because typescript
const BuildingsControl = lazyControlWrapper(() => import("../buildings/controls/BuildingsListControl").then(i => ({default: i.BuildingsListControl})), undefined);

const BuildingsList: FC = () => {
    useAdminModeMenu("manage buildings");
    useAppBarOptions("buildings");
    return <Grid container justify="center">
        <Grid item xs={12} md={8}>
            <BuildingsControl/>
        </Grid>
    </Grid>;
};

const RotaServicesList: FC = () => {
    //useAdminModeMenu("manage buildings");
    useAppBarOptions("rota services");
    const {applicationRootPath} = window.applicationProperties;
    return <Grid container justify="center" style={{paddingTop: "25px"}}>
            <Grid item xs={12} md={8}>
                <ServicesProjectsBuildingsSelector applicationRootPath={applicationRootPath}/>
            </Grid>
        </Grid>;
};

const SchedulerPage: FC = () => {
    //useAdminModeMenu("manage buildings");
    useAppBarOptions("scheduler");
    const {applicationRootPath} = window.applicationProperties;
    return <Grid container justify="center" style={{paddingTop: "25px"}}>
        <Grid item xs={11}>
            <SchedulerView/>
        </Grid>
    </Grid>;
};

// NB could use QuickLog approach also
//const MyReferralsList = React.lazy(() => import("../referral/components/QuickLog"))
// referrals by status uses ReferralsListControl
// @ts-ignore - because typescript
/**
 * 'referrals by status' menu - no-arg control
 */
const ReferralsListControl = lazyControlWrapper(() => import("../referral/controls/ReferralsListControl"));
const ReferralsListByStatus: FC = () => {
    useAppBarOptions("referrals by status")
    return <ReferralsListControl/>
}

const BuildingOverview: FC = () => {
    useAdminModeMenu("config mode");
    const {buildingId} = useParams<{buildingId: string}>();
    const titleEl = useRef<HTMLDivElement>();
    const section1 = useRef<HTMLDivElement>();
    const section2 = useRef<HTMLDivElement>();

    const sidebar: OverviewSummary = {
        setTitle: (title: string) => { $(titleEl.current).empty().append(title); },
        getSection1: () => $(section1.current),
        getSection2: () => $(section2.current)
    };

    useAppBarOptions(<span ref={titleEl}/>);

    // @ts-ignore
    const Component = lazyControlWrapper(() => import("../buildings/controls/BuildingOverviewControl"), buildingId, sidebar);
    return <>
            {/*TODO: This is what goes into Sidebar in old code*/}
            <div className="container-fluid">
                <EventBusDisplayContainer bus={MenuUpdateEvent.bus}/>
                <div ref={section1}/>
                <div ref={section2}/>
            </div>
            <Component/>
        </>;
}

class MenuBuilder extends SidebarMenuBuilder {
    constructor(base:string, private sessionData: SessionData) {
        super(base);
    }

    addStaffRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                          disabled?: boolean) {
        return super.addOwnRoute(text, iconClasses, path, condition && this.sessionData.hasRoleStaff(), secondaryText, disabled)
    }

    /**
     * Add a react router route menu item only visible to staff who have it as the 'top level group' permission.
     * Actually, we mean staff, managers and senior managers, but we don't mean carer, daily checks or user admin etc.
     * The 'top level group' can be assumed to have a different menu, but retain the staff access on the API server-side.
     * If we need finer grained permission at some point in the future, we can create 'ui' permissions.
     * TODO we can now start to carefully move menus over from addStaffRoute
     * TODO user-perceived access needs tying in with UserForm.tsx popup help
     */
    addTopLevelStaffRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                          disabled?: boolean, backgroundColour?: string) {
        return super.addOwnRoute(text, iconClasses, path, condition &&
                this.sessionData.hasTopLevelGroup('staff', true)
                && !this.sessionData.hasRoleSecurity()
            , secondaryText, disabled, backgroundColour
        )
    }
    addTopLevelManagerRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                          disabled?: boolean) {
        return super.addOwnRoute(text, iconClasses, path, condition &&
                this.sessionData.hasTopLevelGroup('manager', true)
                && !this.sessionData.hasRoleSecurity()
                , secondaryText, disabled)
    }

    addTopLevelRotaRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                         disabled?: boolean) {
        return super.addOwnRoute(text, iconClasses, path, condition && this.sessionData.hasTopLevelGroup('rota', false), secondaryText, disabled)
    }

    addTopLevelCarerRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                           disabled?: boolean) {
        return super.addOwnRoute(text, iconClasses, path, condition && this.sessionData.hasTopLevelGroup('carer', false), secondaryText, disabled)
    }

    /** Add a react router route menu item only visible to clients */
    addTopLevelClientRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                disabled?: boolean) {
        return super.addOwnRoute(text, iconClasses, path, condition && this.sessionData.hasRoleClient(), secondaryText, disabled)
    }

    /** Add a react router route menu item only visible to clients */
    addTopLevelCommissionerRoute(text: string, iconClasses: string, path: string, condition: boolean = true, secondaryText?: string,
                   disabled?: boolean) {
        return super.addOwnRoute(text, iconClasses, path, condition && this.sessionData.hasRoleCommissioner(), secondaryText, disabled)
    }
}


const MainMenuItems: FC<{base: string}> = ({base}) => { // Base must start and end in / (i.e. /r/welcome/ or / )
    const {sessionData} = useServicesContext();
    const minWidth768 = useMediaQuery("(min-width: 768px)");

    return new MenuBuilder(base, sessionData)
        .addOwnRoute("test pages (webpack only)", "fa fa-user", "test",
                base == "/" && sessionData.hasRoleSysAdmin(), "NOTE: run staff app for testing referral/care features")
        .addOwnRoute("test referral (webpack only)", "fa fa-file-code-o", "test-sr2",
                base == "/" && sessionData.hasRoleSysAdmin())

        .addSubHeader("clients", sessionData.hasTopLevelGroup('staff', true), blueAppBarLight)
            .addTopLevelStaffRoute("find/new", "fa fa-search", "", undefined, undefined, undefined)
            .addTopLevelStaffRoute("my/team live", "fa fa-user", "myteam/", undefined, undefined, undefined)
            .addTopLevelStaffRoute("list by status", "fa fa-user", "referrals/by-service/", undefined, undefined, undefined)
            .addTopLevelStaffRoute("group support", "fa fa-group", "group-support/",
                                   sessionData.isEnabled("menu.groupsupport"), // was module "community"
                                   undefined, undefined)
            // see Sr2AppBar for same menu icon
            .addTopLevelStaffRoute("group comms", "fa fa-comments-o", "group-comms/",
                   sessionData.isEnabled("menu.groupcomms"), undefined, undefined)
        .addDivider("end-clients", sessionData.hasTopLevelGroup('staff', true))

        .addSubHeader("buildings", sessionData.hasTopLevelGroup('staff', true), greenAppBarLight)
            .addTopLevelStaffRoute("buildings", "fa fa-home", "buildings/?showChildren=false",
               sessionData.isEnabled("menu.buildings"), undefined, undefined
            )
            .addTopLevelStaffRoute("occupancy", "fa fa-home", "buildings/occupancy",
               sessionData.isEnabled("menu.buildings.occupancy"), undefined, undefined
            )
            .addOwnRoute("repairs", "fa fa-wrench", "repairs/",
               sessionData.isEnabled("menu.repairs") && sessionData.hasRoleRepairs(),
                 undefined, undefined)
        .addDivider("end-buildings", sessionData.hasTopLevelGroup('staff', true))

        .addOwnRoute("incidents", "fa fa-medkit", "incidents/",
            sessionData.isEnabled("menu.incidents")
            && sessionData.hasRoleIncidents())
        .addTopLevelRotaRoute("rota", "fa fa-table", "rota/all/day",
            sessionData.isModuleEnabled("rota")
            && sessionData.isEnabled("rota.whole-org.enable")
        )
        // new component of services associated with buildings
        // ideally as tickboxes to build a rota page, but for now just list one
        .addTopLevelRotaRoute("rota", "fa fa-table", "rota/services",
            sessionData.isModuleEnabled("rota")
                && sessionData.isEnabled("rota.services")
        )
        .addTopLevelRotaRoute("scheduler", "fa fa-table", "scheduler",
            sessionData.isModuleEnabled("rota")
                && sessionData.isEnabled("rota.scheduler")
        )
        .addOwnRoute("contracts", "fa fa-file-text-o", "contracts",
             sessionData.hasRoleFinance() // TODO: better icon - and submenu
        )
        .addOwnRoute("billing", "fa fa-gbp", "billing",
             sessionData.hasRoleFinance()
        )

        .addTopLevelStaffRoute("calendar", "fa fa-calendar", "cal",
           !sessionData.isEnabled("menu.calendar.hide"))

        .addDivider("1")
        // r/app is the default app in ecco-staff-app/index.html, which loads WelcomeAppBar
        // which is <App>, unless carer & staffAppIsPrintableAppts in which case <CareAppBar>
        .addExternalRoute(sessionData.isModuleEnabled("offline") ? "offline" : "staff app",
                          "fa fa-tablet", "r/app/",
                          sessionData.hasRoleStaff() && (sessionData.isModuleEnabled("offline")))
        // if we are carer and feature is on - we'll go to care/ <CarePage> below
        // if we are staff with ROLE_DAILYCHECKS - we'll go to dailyChecks/ <DailyChecksPage> below
        // if we are staff and feature is on - we'll go to events/ <EventPage> below
        // See careSingleVisitDataLoader transformToCareVisit.

        .addTopLevelCarerRoute("visits", "fa fa-tablet", "care")
        .addTopLevelStaffRoute("daily app", "fa fa-tablet", "dailyChecks",
            sessionData.hasRoleDailyChecks())
        .addTopLevelStaffRoute("visits", "fa fa-tablet", "events",
            sessionData.isEnabled("rota.events"))
        .addOwnRoute("my/team live", "fa fa-user", "myteam/",
             sessionData.hasRoleSecurity())

        .addDivider("2")
        .addTopLevelStaffRoute("dashboards", "fa fa-dashboard", "dashboards/")
        .addOwnRoute("reports", "fa fa-pie-chart", "reports/",
            sessionData.hasRoleReports() && minWidth768
        )
        // add a disabled menu item to remember why its gone on smaller devices (or in f12)
        .addOwnRoute("reports", "fa fa-pie-chart", "reports/",
            sessionData.hasRoleReports() && !minWidth768, undefined, !minWidth768
        )

        .addDivider("3")
        .addOwnRoute("admin", "fa fa-cog", "admin",
            sessionData.hasRoleAdmin() || sessionData.hasRoleReferralDelete() || sessionData.hasRoleGroupSupportAdmin() || sessionData.hasRoleUserAdmin()
        )
        .addOwnRoute("config", "fa fa-cog", "settings",
            sessionData.hasRoleSysAdmin() || sessionData.hasRoleAdmin()
        )
        .build();
};

function deriveOverrides() {
    const params = useQuery();
    const startDate = params.get("startDate");
    const endDate = params.get("endDate");
    // Note: Rather hacky in that both start/endDate is a special case and we then force other props to null
    const override: Partial<SelectionCriteria> = startDate && endDate
        ? {
            selectorType: null,
            relativeStartIndex: null,
            relativeEndIndex: null,
            absoluteFromDate: startDate,
            absoluteToDate: endDate
        }
        : undefined;
    return override;
}

const ChartHack = () => { // TODO: Move into Chart
    const {chartUuid} = useParams<{chartUuid: string}>();
    const override = deriveOverrides();
    return <Chart chartUuid={chartUuid} override={override} />;
};

function WelcomeAppBar() {
    return <AppBarContextProvider>
        <CareVisitMenuContextProvider>
            <WelcomeAppBarBase/>
        </CareVisitMenuContextProvider>
    </AppBarContextProvider>
}

function WelcomeAppBarBase() {
    const {sessionData} = useServicesContext();
    let {path} = useRouteMatch(); // e.g. /nav/r/welcome or /nav/w/welcome (for wide)
    path = path == "/" ? "/" : path + "/";
    const history = useHistory();
    const wideMode = path.startsWith("/nav/w/");
    console.debug(`WelcomeAppBar path=${path}`);

    const ctx = useAppBarContext();
    if (!ctx) {
        return null;
    }

    const guidanceMenu = sessionData.isEnabled("menu.guidance") && <GuidanceMenu uuid={"*************-babe-babe-dadafee1600d"}/>;
    const qrMenu = sessionData.isEnabled("menu.qr") && <QRScanMenu/>
    const userMenu = <UserMenu extraMenuItems={ctx.extraMenuItems} hideUsername={guidanceMenu != null || qrMenu != null}/>;
    const menuRight = [guidanceMenu, qrMenu, userMenu];

    return (
        <AsyncSessionData promiseFn={SessionDataService.getFeatures}>
            <AppBarBase
                appColour={ctx.appColour}
                title={ctx.header || " "} // If we use an empty string things go wrong - see AppBarBase {props.title ?
                right={ctx.menus || menuRight}
                drawerContent={<MainMenuItems base={path}/>}
                wideMode={wideMode}
                onLogoClick={() => history.push(`${path}`)}
            >
                <ServicesContextProvider>
                    <Switch>
                        <Route path={`${path}cal`}>
                            <div style={{maxWidth: 1340, marginLeft: "auto", marginRight: "auto"}}>
                                <CalendarWithStaffOverlay/>
                            </div>
                        </Route>
                        {/* does this work? */}
                        <Route path={`${path}care/:date?`}>
                            <CarePage />
                        </Route>
                        <Route path={`${path}events/:date?`}>
                            <EventPage />
                        </Route>
                        <Route path={`${path}dailyChecks`}>
                            <DailyChecksPage />
                        </Route>
                        <Route exact path={`${path}rota/:type/:resourceFilter/:demandFilter`}>
                            {/*e.g. type=day|week demandResourceFilter=workers:all srFilter = referrals:all*/}
                            {({match}) => <RotaView {...match.params}/>}
                        </Route>
                        <Route exact path={`${path}runs/:type/:resourceFilter/:demandFilter`}>
                            {({match}) => <RotaView {...match.params}/>}
                        </Route>
                        <Route exact path={`${path}rota/all/:type`}>
                            {({match}) => <RotaView {...match.params}
                                                    demandFilter="referrals:all" resourceFilter="workers:all"
                            />}
                        </Route>

                        <Route exact path={`${path}buildings/:buildingId/rota/:type`}>
                            {({match}) => <RotaView {...match.params}
                                                    resourceFilter="workers:all"
                                                    demandFilter={`buildings:${match.params.buildingId}`}
                            />}
                        </Route>
                        <Route exact path={`${path}buildings/:buildingId/runs/:type`}>
                            {({match}) => <RotaView {...match.params}
                                                    resourceFilter="careruns:all"
                                                    demandFilter={`buildings:${match.params.buildingId}`}
                            />}
                        </Route>

                        <Route exact path={`${path}rota/services`}>
                            <RotaServicesList/>
                        </Route>

                        <Route exact path={`${path}scheduler`}>
                            <SchedulerPage/>
                        </Route>

                        <Route path={`${path}billing`}><AppBilling/></Route>
                        <Route exact path={`${path}reports`}>
                            <ReportsList/>
                        </Route>
                        <Route path={`${path}reports/:chartUuid`}>
                            <ChartHack/>
                        </Route>
                        <Route exact path={[`${path}buildings`, `${path}building`]}>
                            <BuildingsList/>
                        </Route>
                        {/*<Route exact path={`${path}buildings/:buildingId`}><BuildingOverview/></Route>
                        <Route exact path={`${path}building/:buildingId`}>{
                            // NB useAppBarOptions taken out (see commit)
                            ({match}) => <BuildingView {...match.params}/>
                        }</Route>*/}
                        <Route exact path={`${path}shifts/:buildingId`}>{
                            ({match}) => <ShiftView
                                {...match.params}
                            />
                        }</Route>

                        <Route exact path={`${path}buildings/occupancy`}>
                            <OccupancyList/>
                        </Route>

                        <Route exact path={`${path}group-support`}>
                            <Box p={2}>
                                <EventBusDisplayContainer bus={MenuUpdateEvent.bus}/>
                                <GroupSupportActivityList/>
                            </Box>
                        </Route>
                        <Route exact path={`${path}group-support/:activityId/`}>
                            <GroupSupportActivityOverviewModal/>
                        </Route>

                        <Route exact path={`${path}group-comms`}>
                            <Box p={2}>
                                <EventBusDisplayContainer bus={MenuUpdateEvent.bus}/>
                                <GroupCommsActivityList/>
                            </Box>
                        </Route>
                        <Route exact path={`${path}group-comms/:activityId/`}>
                            <GroupCommsActivityOverviewModal/>
                        </Route>

                        <Route exact path={`${path}incidents`}>
                            <Box p={2}>
                                <EventBusDisplayContainer bus={MenuUpdateEvent.bus}/>
                                <NewIncident/>
                                <IncidentSchemaList/>
                            </Box>
                        </Route>
                        <Route exact path={`${path}repairs`}>
                            <Box p={2}>
                                <EventBusDisplayContainer bus={MenuUpdateEvent.bus}/>

                                <RepairSchemaList/>
                            </Box>
                        </Route>

                        <Route exact path={`${path}group-aux/:activityId/`}>
                            <GroupAuxActivityOverviewModal/>
                        </Route>

                        <Route exact path={`${path}contracts`}><ContractsList/></Route>
                        <Route exact path={`${path}contracts/:contractId`}>{
                            ({match}) => <ContractView {...match.params}/>
                        }</Route>
                        <Route exact path={`${path}referrals/by-service/`}>
                            <Box p={2}>
                                <EventBusDisplayContainer bus={MenuUpdateEvent.bus}/>
                                <ReferralsListByStatus/>
                            </Box>
                        </Route>
                        <Route path={`${path}myteam`}><MyReferralsList/></Route>
                        <Route exact path={`${path}`}>
                            {sessionData.hasTopLevelGroup('staff', true)
                                    ? <WelcomePage/>
                                    : null}
                        </Route>
                        <RouteFallbackWithDiagnostics/>
                    </Switch>
                </ServicesContextProvider>
            </AppBarBase>
        </AsyncSessionData>
    );
}

export default WelcomeAppBar;